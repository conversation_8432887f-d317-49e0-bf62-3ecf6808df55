import argparse
import os
import mlflow
import logging
from typing import Dict

def setup_logging():
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description='Register trained sensor models in MLflow')
    parser.add_argument('--model_path', type=str, required=True,
                      help='path to the trained models')
    parser.add_argument('--model_name', type=str, required=True,
                      help='name for the registered models')
    parser.add_argument('--model_version', type=str, default="V3", choices=["V3", "V4"],
                      help='Model version to register (V3 or V4)')
    parser.add_argument('--experiment_name', type=str, default="model_registration",
                      help='Name of the MLflow experiment')
    return parser.parse_args()

def register_sensor_model(
    sensor_name: str,
    model_path: str,
    model_base_name: str,
    model_version: str,
    experiment_name: str,
    logger: logging.Logger
) -> Dict:
    """Register a single sensor model in MLflow"""
    try:
        # Construct model name and path
        model_name = f"{model_base_name}_{sensor_name}"
        full_model_path = os.path.join(model_path, f"{sensor_name}_model_{model_version}")

        # Verify model exists
        if not os.path.exists(full_model_path):
            raise FileNotFoundError(f"Model not found: {full_model_path}")

        logger.info(f"Loading {model_version} model from: {full_model_path}")
        model = mlflow.lightgbm.load_model(full_model_path)

        # Create individual MLflow run for each sensor model to avoid conflicts
        current_experiment = mlflow.get_experiment_by_name(experiment_name)
        if current_experiment is None:
            experiment_id = mlflow.create_experiment(experiment_name)
        else:
            experiment_id = current_experiment.experiment_id

        # Register model in its own run
        with mlflow.start_run(experiment_id=experiment_id, run_name=f"{sensor_name}_registration") as run:
            logger.info(f"Registering {sensor_name} in run: {run.info.run_id}")

            # Set model metadata tags
            mlflow.set_tags({
                "sensor_type": sensor_name,
                "model_version": model_version,
                "model_type": "lightgbm"
            })

            # Register the model
            mlflow.lightgbm.log_model(
                lgb_model=model,
                artifact_path="model",
                registered_model_name=model_name
            )

            logger.info(f"Successfully registered {sensor_name} as {model_name}")
            return {
                "sensor_name": sensor_name,
                "model_name": model_name,
                "run_id": run.info.run_id,
                "experiment": experiment_name,
                "status": "registered"
            }

    except Exception as e:
        logger.error(f"Failed to register {sensor_name}: {str(e)}")
        return {
            "sensor_name": sensor_name,
            "status": "failed",
            "error": str(e)
        }

def register_all_models(model_path: str, model_base_name: str, model_version: str, experiment_name: str, logger: logging.Logger):
    """Register all sensor models"""
    sensors = ['Kitting_Humidity', 'Kitting_Temperature', 'Kitting_Pressure']
    registration_results = []

    for sensor in sensors:
        logger.info(f"Registering {model_version} model for {sensor}...")
        result = register_sensor_model(
            sensor_name=sensor,
            model_path=model_path,
            model_base_name=model_base_name,
            model_version=model_version,
            experiment_name=experiment_name,
            logger=logger
        )
        registration_results.append(result)

    return registration_results

def main():
    logger = setup_logging()
    args = parse_args()


    # Clean up any lingering MLflow runs
    try:
        if mlflow.active_run():
            mlflow.end_run()
    except Exception:
        pass

    # Verify model path exists
    if not os.path.exists(args.model_path):
        logger.error(f"Model path does not exist: {args.model_path}")
        return

    logger.info(f"Registering {args.model_version} models from path: {args.model_path}")
    registration_results = register_all_models(args.model_path, args.model_name, args.model_version, args.experiment_name, logger)

    # Summary
    registered = sum(1 for r in registration_results if r["status"] == "registered")
    failed = sum(1 for r in registration_results if r["status"] == "failed")

    logger.info(f"\nRegistration Summary:")
    logger.info(f"Total models: {len(registration_results)}")
    logger.info(f"Successfully registered: {registered}")
    logger.info(f"Failed: {failed}")

    # Log details for each model
    for result in registration_results:
        logger.info(f"  {result['sensor_name']}: {result['status']}")

if __name__ == "__main__":
    main()