import argparse
import os
import mlflow
import json
import logging
from typing import Dict, List

def setup_logging():
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description='Register trained sensor models in MLflow')
    parser.add_argument('--model_path', type=str, required=True,
                      help='path to the trained models')
    parser.add_argument('--model_name', type=str, required=True,
                      help='name for the registered models')
    parser.add_argument('--experiment_name', type=str, default="model_registration",
                      help='Name of the MLflow experiment')
    return parser.parse_args()
   

def register_sensor_model(
    sensor_name: str,
    model_path: str,
    model_base_name: str,
    experiment_name: str,
    logger: logging.Logger
) -> Dict:
    """Register a single sensor model in MLflow"""
    try:
        # Set the experiment
        mlflow.set_experiment(experiment_name)

        # Construct model name with sensor type
        model_name = f"{model_base_name}_{sensor_name}"

        # Load the model - try both V3 and V4 versions
        model_v3_path = os.path.join(model_path, f"{sensor_name}_model_V3")
        model_v4_path = os.path.join(model_path, f"{sensor_name}_model_V4")

        # Determine which version exists
        if os.path.exists(model_v4_path):
            full_model_path = model_v4_path
            logger.info(f"Found V4 model, using: {full_model_path}")
        elif os.path.exists(model_v3_path):
            full_model_path = model_v3_path
            logger.info(f"Found V3 model, using: {full_model_path}")
        else:
            logger.error(f"No model found for {sensor_name}. Checked V3 and V4 paths.")
            raise FileNotFoundError(f"No model found for {sensor_name}")

        # List directory contents to debug
        if os.path.exists(model_path):
            logger.info(f"Contents of model directory: {os.listdir(model_path)}")

        # Check what's inside the specific model directory
        if os.path.exists(full_model_path):
            logger.info(f"Contents of {full_model_path}: {os.listdir(full_model_path)}")
        else:
            logger.warning(f"Model path does not exist: {full_model_path}")

        model = mlflow.lightgbm.load_model(full_model_path)

        # Register model without creating a new MLflow run
        # Work within the existing Azure ML pipeline MLflow context
        try:
            # Check if we're in an active run
            current_run = mlflow.active_run()
            if current_run:
                logger.info(f"Using existing MLflow run: {current_run.info.run_id}")
                run_id = current_run.info.run_id

                # Set tags for this sensor within the existing run
                mlflow.set_tags({
                    f"{sensor_name}_sensor_type": sensor_name,
                    f"{sensor_name}_model_version": "V3",
                    f"{sensor_name}_model_type": "lightgbm"
                })
            else:
                logger.info("No active MLflow run found")
                run_id = "no_active_run"

            # Try to register the model directly (without creating a new run)
            try:
                mlflow.lightgbm.log_model(
                    lgb_model=model,
                    artifact_path=f"model_{sensor_name}",
                    registered_model_name=model_name
                )
                logger.info(f"Model registered successfully for {sensor_name} with name: {model_name}")
                registration_status = "registered"

            except Exception as reg_error:
                logger.warning(f"Model registration failed, falling back to logging only: {str(reg_error)}")
                # Fallback to just logging the model without registration
                mlflow.lightgbm.log_model(
                    lgb_model=model,
                    artifact_path=f"model_{sensor_name}"
                )
                logger.info(f"Model logged (but not registered) successfully for {sensor_name}")
                registration_status = "logged_only"

        except Exception as context_error:
            logger.error(f"Error working with MLflow context for {sensor_name}: {str(context_error)}")
            # Try direct registration without any MLflow run context
            try:
                # Register directly using MLflow client
                client = mlflow.tracking.MlflowClient()

                # Create a temporary run just for registration
                experiment = mlflow.get_experiment_by_name(experiment_name)
                if experiment is None:
                    experiment_id = mlflow.create_experiment(experiment_name)
                else:
                    experiment_id = experiment.experiment_id

                temp_run = client.create_run(experiment_id)
                run_id = temp_run.info.run_id

                # Log and register the model
                mlflow.lightgbm.log_model(
                    lgb_model=model,
                    artifact_path=f"model_{sensor_name}",
                    registered_model_name=model_name,
                    run_id=run_id
                )

                client.set_terminated(run_id)
                logger.info(f"Model registered using temporary run for {sensor_name}")
                registration_status = "registered_temp_run"

            except Exception as final_error:
                logger.error(f"All registration attempts failed for {sensor_name}: {str(final_error)}")
                run_id = "registration_failed"
                registration_status = "failed"

        logger.info(f"Registration completed for {sensor_name}. Status: {registration_status}")

        return {
            "sensor_name": sensor_name,
            "model_name": model_name,
            "run_id": run_id,
            "experiment": experiment_name,
            "status": registration_status
        }

    except Exception as e:
        logger.error(f"Failed to register model for {sensor_name}: {str(e)}")
        return {
            "sensor_name": sensor_name,
            "status": "failed",
            "error": str(e)
        }

def register_all_models(model_path: str, model_base_name: str, experiment_name: str, logger: logging.Logger):
    """Register all sensor models"""
    sensors = ['Kitting_Humidity', 'Kitting_Temperature', 'Kitting_Pressure']
    registration_results = []

    for sensor in sensors:
        logger.info(f"Registering model for {sensor}...")
        result = register_sensor_model(
            sensor_name=sensor,
            model_path=model_path,
            model_base_name=model_base_name,
            logger=logger,
            experiment_name=experiment_name
        )
        registration_results.append(result)

    return registration_results

def main():
    logger = setup_logging()
    args = parse_args()


    # Clean up any lingering MLflow runs from previous components
    try:
        if mlflow.active_run():
            logger.info("Cleaning up existing MLflow run before starting registration...")
            mlflow.end_run()
    except Exception as e:
        logger.info(f"MLflow cleanup completed: {e}")

    # Log environment variables to help debug
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Model path argument: {args.model_path}")

    # Ensure model path exists
    model_path = args.model_path
    if not os.path.exists(model_path):
        logger.warning(f"Model path does not exist: {model_path}")
        # Try to list parent directory
        parent_dir = os.path.dirname(model_path)
        if os.path.exists(parent_dir):
            logger.info(f"Contents of parent directory: {os.listdir(parent_dir)}")
    else:
        logger.info(f"Model path exists. Contents: {os.listdir(model_path)}")

    logger.info(f"Registering models from path: {args.model_path}")
    registration_results = register_all_models(args.model_path, args.model_name, args.experiment_name, logger)

    # Summary
    registered = sum(1 for r in registration_results if r["status"] in ["registered", "registered_temp_run"])
    logged_only = sum(1 for r in registration_results if r["status"] == "logged_only")
    failed = sum(1 for r in registration_results if r["status"] == "failed")

    logger.info(f"\nRegistration Summary:")
    logger.info(f"Total models: {len(registration_results)}")
    logger.info(f"Successfully registered: {registered}")
    logger.info(f"Logged only (not registered): {logged_only}")
    logger.info(f"Failed: {failed}")

    # Log details for each model
    for result in registration_results:
        status_msg = result['status']
        if 'run_id' in result:
            status_msg += f" (run: {result['run_id'][:8]}...)"
        logger.info(f"  {result['sensor_name']}: {status_msg}")


if __name__ == "__main__":
    main()
# python register.py --model_path ../../Model --model_name sensor_model --experiment_name test_exp